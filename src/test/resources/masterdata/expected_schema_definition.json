{"$schema": "http://json-schema.org/draft-07/schema#", "title": "External Fleet Master Vehicle", "type": "object", "additionalProperties": false, "properties": {"vehicle": {"$ref": "#/definitions/ExternalVehicle"}}, "required": ["vehicle"], "definitions": {"ExternalVehicle": {"type": "object", "additionalProperties": false, "properties": {"vguid": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "vin": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "equiId": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "equipmentNumber": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "status": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "production": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleProduction"}]}, "model": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleModel"}]}, "price": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehiclePrice"}]}, "registration": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleRegistration"}]}, "transfers": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "array", "items": {"$ref": "#/definitions/ExternalVehicleTransferWithStatus"}}]}, "consumption": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalConsumptionInfo"}]}, "wltp": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleWltpInfo"}]}, "currentMileage": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalCurrentMileage"}]}, "order": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalOrderInfo"}]}, "fleet": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalFleetInfo"}]}, "evaluation": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleEvaluationData"}]}, "sales": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleSalesData"}]}, "technical": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleTechnicalData"}]}, "color": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleColorData"}]}, "options": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "object", "additionalProperties": {}}]}}}, "ExternalVehicleProduction": {"type": "object", "additionalProperties": false, "properties": {"number": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "factory": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "zp8Date": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "plannedEndDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "technicalModelYear": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "gearBoxClass": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}, "ExternalVehicleModel": {"type": "object", "additionalProperties": false, "properties": {"orderType": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "modelRange": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "manufacturer": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "vehicleType": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "description": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}, "ExternalVehiclePrice": {"type": "object", "additionalProperties": false, "properties": {"vehicleFactoryGrossPriceEUR": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "vehicleFactoryNetPriceEUR": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "grossPriceWithExtras": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}}}, "ExternalVehicleRegistration": {"type": "object", "additionalProperties": false, "properties": {"firstRegistrationDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "currentLicensePlate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "testNumber": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "hsn": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "tsn": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "sfme": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}}}, "ExternalVehicleTransferWithStatus": {"type": "object", "additionalProperties": false, "properties": {"status": {"type": "string"}, "transfers": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "array", "items": {"$ref": "#/definitions/ExternalVehicleTransfer"}}]}}, "required": ["status"]}, "ExternalVehicleTransfer": {"type": "object", "additionalProperties": false, "properties": {"vehicleResponsiblePerson": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleResponsiblePerson"}]}, "deliveryIndex": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "vehicleTransferKey": {"type": "integer"}, "deliveryDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "returnDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "leasingPrivilege": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "vehicleUsage": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalVehicleUsage"}]}, "usageGroup": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalUsageGroup"}]}, "licensePlates": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "array", "items": {"$ref": "#/definitions/LicensePlatePeriod"}}]}, "mileageAtDelivery": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "mileageAtReturn": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "utilizationArea": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "maintenanceOrderNumber": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "usingCostCenter": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "depreciationRelevantCostCenterId": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "uuid"}]}, "internalOrderNumber": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "plannedDeliveryDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "leasingArt": {"oneOf": [{"type": "null", "title": "Not included"}, {"$ref": "#/definitions/ExternalLeasingArt"}]}}, "required": ["vehicleTransferKey"]}, "ExternalVehicleResponsiblePerson": {"type": "object", "additionalProperties": false, "properties": {"employeeNumber": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "department": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}, "ExternalVehicleUsage": {"type": "object", "additionalProperties": false, "properties": {"usage": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}, "ExternalUsageGroup": {"type": "object", "additionalProperties": false, "properties": {"description": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}, "LicensePlatePeriod": {"type": "object", "additionalProperties": false, "properties": {"licensePlate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "fromDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}, "toDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}}}, "ExternalLeasingArt": {"type": "object", "additionalProperties": false, "properties": {"id": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "description": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "pool": {"type": "boolean"}}, "required": ["pool"]}, "ExternalConsumptionInfo": {"type": "object", "additionalProperties": false, "properties": {"driveType": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "typification": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "primaryFuelType": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "enum": ["E10", "SUPER", "REGULAR", "DIESEL", "ELECTRIC"]}]}, "secondaryFuelType": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "enum": ["E10", "SUPER", "REGULAR", "DIESEL", "ELECTRIC"]}]}}}, "ExternalVehicleWltpInfo": {"type": "object", "additionalProperties": false, "properties": {"co2Combined": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "electricRangeCity": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "electricRange": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}}}, "ExternalCurrentMileage": {"type": "object", "additionalProperties": false, "properties": {"mileage": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "readDate": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string", "format": "date-time"}]}}}, "ExternalOrderInfo": {"type": "object", "additionalProperties": false, "properties": {"blockedForSale": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}, "primaryStatus": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "tradingPartnerNumber": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}, "ExternalFleetInfo": {"type": "object", "additionalProperties": false, "properties": {"scrapVehicle": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}, "residualValueMarket": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}, "raceCar": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}, "classic": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}}}, "ExternalVehicleEvaluationData": {"type": "object", "additionalProperties": false, "properties": {"appraisalNetPrice": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}}}, "ExternalVehicleSalesData": {"type": "object", "additionalProperties": false, "properties": {"reservedForB2C": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}, "contractSigned": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "boolean"}]}}}, "ExternalVehicleTechnicalData": {"type": "object", "additionalProperties": false, "properties": {"amountSeats": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "acceleration0100KmhLaunchControl": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "acceleration0100Kmh": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "acceleration80120Kmh": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "cargoVolume": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "chargingTimeAc11Kw0100": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "chargingTimeAc22Kw": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "chargingTimeAc96Kw0100": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "chargingTimeDcMaxPower580": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "engineCapacity": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "curbWeightDin": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "curbWeightEu": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "grossBatteryCapacity": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "grossVehicleWeight": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "height": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "length": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "maximumChargingPowerDc": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "maximumPayload": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "maxRoofLoadWithPorscheRoofTransportSystem": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "netBatteryCapacity": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "number"}]}, "powerKw": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "topSpeed": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "totalPowerKw": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "vehicleWidthMirrorsExtended": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}, "widthMirrorsFolded": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "integer"}]}}}, "ExternalVehicleColorData": {"type": "object", "additionalProperties": false, "properties": {"exterior": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "exteriorDescription": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "interior": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}, "interiorDescription": {"oneOf": [{"type": "null", "title": "Not included"}, {"type": "string"}]}}}}}