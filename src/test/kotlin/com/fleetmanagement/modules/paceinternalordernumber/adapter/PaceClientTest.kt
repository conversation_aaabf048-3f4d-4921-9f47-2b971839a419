/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.paceinternalordernumber.adapter

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest.model.*
import com.github.tomakehurst.wiremock.client.WireMock.*
import com.github.tomakehurst.wiremock.junit5.WireMockTest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsString
import org.hamcrest.Matchers.instanceOf
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.security.oauth2.core.OAuth2AuthorizationException
import org.springframework.test.context.TestPropertySource
import java.io.BufferedReader
import java.time.LocalDate

@WireMockTest(httpPort = 8092)
@SpringBootTest
@Import(TestcontainersConfiguration::class)
@TestPropertySource(
    properties = ["pace.base-url=http://localhost:8092"],
)
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class PaceClientTest {
    private val tokenResponse =
        ClassPathResource("token_response.json").inputStream.bufferedReader().use(BufferedReader::readText)

    private val objectMapper = ObjectMapper().registerModules(JavaTimeModule())

    val emptyResponse =
        InternalOrderNumberDto(
            n0YCOINTORDERREPLICATIONFMSResponse = FMSResponseDto(),
        )
    val requestInternalOrderNumberPayload =
        RequestInternalOrderNumberDto(
            COMP_CODE = "0001",
            PLN_CLOSE = "0000-00-00",
            VIN = "someVin",
            APPLICATION_DATE = LocalDate.now().toString(),
            DATE_WORK_BEGINS = "0000-00-00",
            DATE_WORK_ENDS = "0000-00-00",
            COSTCENTER = "H0019137",
            ORDER_NAME = "Rep./Inst. AB-CD_1234 someVin",
            ORDER_TYPE = OrderTypeDto.Y73X,
            PERCENTAGE = "0.00",
            S_ORD_ITEM = "000000",
            SETTL_TYPE = SettlTypeDto.PER,
            PROCESSING_GROUP = "00",
            PERSON_RESP = "00123456",
            PLN_RELEASE = "0000-00-00",
            SOURCE = null,
            RESPCCTR = "00000000",
            PLN_COMPLETION = "0000-00-00",
            CO_AREA = "0001",
            ESTIMATED_COSTS = "0.00",
        )

    @Autowired
    private lateinit var paceClient: PaceClient

    @Test
    @Order(2)
    fun `should request oauth token and set jwt in authorization header`() {
        stubFor(
            post(urlPathMatching("/oauth/token")).willReturn(okForContentType("application/json", tokenResponse)),
        )
        stubFor(
            get(urlPathEqualTo("/http/FMSInternalOrders/3541"))
                .willReturn(okJson(objectMapper.writeValueAsString(emptyResponse))),
        )
        assertDoesNotThrow {
            paceClient.requestOrderNumber(body = requestInternalOrderNumberPayload)
            paceClient.requestOrderNumber(body = requestInternalOrderNumberPayload)
        }
        val serverEvents = getAllServeEvents()
        assertEquals(3, serverEvents.size)
        val employeeRequest2 = serverEvents[0].request
        val employeeRequest1 = serverEvents[1].request
        val tokenRequest = serverEvents[2].request
        val jwt = objectMapper.readTree(tokenResponse)["access_token"].textValue()
        assertThat(tokenRequest.url, containsString("oauth/token"))
        assertEquals(
            "Bearer $jwt",
            employeeRequest1.headers
                .getHeader("Authorization")
                .values()
                .single(),
        )
        assertEquals(
            "Bearer $jwt",
            employeeRequest2.headers
                .getHeader("Authorization")
                .values()
                .single(),
        )
    }

    @Test
    @Order(1)
    fun `should throw PaceException when token could not be obtained`() {
        stubFor(
            post(urlPathMatching("/oauth/token")).willReturn(unauthorized()),
        )
        stubFor(
            get(urlPathEqualTo("/http/FMSInternalOrders/3541"))
                .willReturn(okJson(objectMapper.writeValueAsString(emptyResponse))),
        )

        val exception =
            assertThrows<PaceException> {
                paceClient.requestOrderNumber(body = requestInternalOrderNumberPayload)
            }
        assertThat(exception.cause, instanceOf(OAuth2AuthorizationException::class.java))
    }

    @Test
    @Order(3)
    fun `should request new order number`() {
        val response =
            InternalOrderNumberDto(
                n0YCOINTORDERREPLICATIONFMSResponse =
                    FMSResponseDto(
                        EV_OBJNR = "orderNumber",
                    ),
            )
        stubFor(
            post(urlPathMatching("/oauth/token")).willReturn(okForContentType("application/json", tokenResponse)),
        )
        stubFor(
            get(urlPathEqualTo("/http/FMSInternalOrders/3541"))
                .willReturn(okJson(objectMapper.writeValueAsString(response))),
        )
        val orderNumberResponseFromClient = paceClient.requestOrderNumber(body = requestInternalOrderNumberPayload)

        assertEquals("orderNumber", orderNumberResponseFromClient.n0YCOINTORDERREPLICATIONFMSResponse.EV_OBJNR)
    }

    @Test
    @Order(4)
    fun `should throw PaceException when PACE returns error status`() {
        stubFor(
            post(urlPathMatching("/oauth/token")).willReturn(okForContentType("application/json", tokenResponse)),
        )
        stubFor(
            get(urlPathEqualTo("/http/FMSInternalOrders/3541"))
                .willReturn(
                    aResponse()
                        .withStatus(500)
                        .withHeader("Content-Type", "application/json")
                        .withBody("You violated our DB constraints."),
                ),
        )

        assertThrows<PaceException> {
            paceClient.requestOrderNumber(body = requestInternalOrderNumberPayload)
        }
    }
}
